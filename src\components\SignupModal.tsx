import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Mail, Lock, User, Building, Globe, ArrowLeft, ArrowRight, Loader2 } from 'lucide-react';
import { supabase } from '../lib/supabase';
import { FaChrome } from 'react-icons/fa';

interface SignupModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface FormData {
  // Step 1
  authMethod: 'google' | 'email';
  email: string;
  password: string;

  // Step 2
  organizationName: string;
  organizationSlug: string;
  organizationDescription: string;

  // Step 3
  adminName: string;
  adminEmail: string;
  adminPassword: string;
}

const SignupModal: React.FC<SignupModalProps> = ({ isOpen, onClose }) => {
  const [currentStep, setCurrentStep] = useState(1);

  useEffect(() => {
    if (isOpen) {
      window.scrollTo({ top: 600, behavior: 'smooth' });
    }
  }, [isOpen]);

  // Handle OAuth callback and check for existing users
  useEffect(() => {
    const handleAuthStateChange = async () => {
      const { data: { session } } = await supabase.auth.getSession();

      if (session?.user && isOpen) {
        // Check if user already exists in our users table
        const { data: existingUser } = await supabase
          .from('users')
          .select('*')
          .eq('id', session.user.id)
          .single();

        if (existingUser) {
          setIsExistingUser(true);
          setError('You are already registered. Please use the login page to access your account.');
          return;
        }

        // User authenticated via OAuth but not in our system yet
        setUserId(session.user.id);
        setFormData(prev => ({
          ...prev,
          authMethod: 'google',
          email: session.user.email || '',
          adminEmail: session.user.email || '',
          adminName: session.user.user_metadata?.full_name || ''
        }));
        setCurrentStep(2);
      }
    };

    if (isOpen) {
      handleAuthStateChange();
    }

    // Listen for auth state changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (event === 'SIGNED_IN' && session?.user && isOpen) {
        // Check if user already exists in our users table
        const { data: existingUser } = await supabase
          .from('users')
          .select('*')
          .eq('id', session.user.id)
          .single();

        if (existingUser) {
          setIsExistingUser(true);
          setError('You are already registered. Please use the login page to access your account.');
          return;
        }

        // User authenticated via OAuth but not in our system yet
        setUserId(session.user.id);
        setFormData(prev => ({
          ...prev,
          authMethod: 'google',
          email: session.user.email || '',
          adminEmail: session.user.email || '',
          adminName: session.user.user_metadata?.full_name || ''
        }));
        setCurrentStep(2);
      }
    });

    return () => {
      subscription.unsubscribe();
    };
  }, [isOpen]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [userId, setUserId] = useState<string | null>(null);
  const [organizationId, setOrganizationId] = useState<string | null>(null);
  const [isExistingUser, setIsExistingUser] = useState(false);

  const [formData, setFormData] = useState<FormData>({
    authMethod: 'email',
    email: '',
    password: '',
    organizationName: '',
    organizationSlug: '',
    organizationDescription: '',
    adminName: '',
    adminEmail: '',
    adminPassword: '',
  });

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    // Clear error when user starts typing
    if (error) setError(null);
  };

  const handleClose = () => {
    // Reset all states when closing
    setCurrentStep(1);
    setLoading(false);
    setError(null);
    setSuccess(false);
    setUserId(null);
    setOrganizationId(null);
    setIsExistingUser(false);
    setFormData({
      authMethod: 'email',
      email: '',
      password: '',
      organizationName: '',
      organizationSlug: '',
      organizationDescription: '',
      adminName: '',
      adminEmail: '',
      adminPassword: '',
    });
    onClose();
  };

  const handleNext = async () => {
    if (currentStep === 1) {
      await handleStep1Authentication();
    } else if (currentStep === 2) {
      await handleStep2Organization();
    } else if (currentStep < 3) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleStep1Authentication = async () => {
    setLoading(true);
    setError(null);

    try {
      if (formData.authMethod === 'google') {
        // Store signup intent in localStorage to restore modal state after redirect
        localStorage.setItem('signup_intent', 'true');
        localStorage.setItem('signup_step', '1');

        const { error } = await supabase.auth.signInWithOAuth({
          provider: 'google',
          options: {
            redirectTo: `${window.location.origin}?signup=true`,
            queryParams: {
              access_type: 'offline',
              prompt: 'consent',
            }
          }
        });

        if (error) throw error;

        // OAuth will redirect, so we don't set step here
        // The useEffect will handle the callback
      } else {
        // Email/password signup
        const { data, error } = await supabase.auth.signUp({
          email: formData.email,
          password: formData.password,
        });

        if (error) throw error;

        if (data.user) {
          setUserId(data.user.id);
          setCurrentStep(2);
        }
      }
    } catch (err: any) {
      setError(err.message || 'Authentication failed');
    } finally {
      setLoading(false);
    }
  };

  const handleStep2Organization = async () => {
    setLoading(true);
    setError(null);

    try {
      // Get current user if not already set
      let currentUserId = userId;
      if (!currentUserId) {
        const { data: { user } } = await supabase.auth.getUser();
        if (user) {
          currentUserId = user.id;
          setUserId(user.id);
        } else {
          throw new Error('User not authenticated');
        }
      }

      // Create organization
      const { data, error } = await supabase
        .from('organizations')
        .insert({
          name: formData.organizationName,
          slug: formData.organizationSlug,
          description: formData.organizationDescription,
          created_by: currentUserId,
          is_active: true
        })
        .select()
        .single();

      if (error) throw error;

      if (data) {
        setOrganizationId(data.uuid);
        setCurrentStep(3);
      }
    } catch (err: any) {
      setError(err.message || 'Failed to create organization');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async () => {
    setLoading(true);
    setError(null);

    try {
      let adminUserId = userId;

      // If we don't have a userId (shouldn't happen), get current user
      if (!adminUserId) {
        const { data: { user } } = await supabase.auth.getUser();
        if (user) {
          adminUserId = user.id;
        } else {
          throw new Error('User not authenticated');
        }
      }

      // Update the existing user record in users table with organization and admin role
      const { error: insertError } = await supabase
        .from('users')
        .insert({
          id: adminUserId,
          email: formData.adminEmail,
          role: 'admin',
          organization_id: organizationId,
          fullname: formData.adminName
        });

      if (insertError) throw insertError;

      console.log('Signup completed successfully');
      console.log('Admin user ID:', adminUserId);
      console.log('Organization ID:', organizationId);

      // Show success message
      setSuccess(true);

    } catch (err: any) {
      setError(err.message || 'Failed to complete setup');
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleSignup = async () => {
    setError(null);
    setIsExistingUser(false);
    setFormData(prev => ({ ...prev, authMethod: 'google' }));
    await handleStep1Authentication();
  };

  const handleSignOut = async () => {
    try {
      await supabase.auth.signOut();
      setUserId(null);
      setCurrentStep(1);
      setError(null);
      setIsExistingUser(false);
      setFormData({
        authMethod: 'email',
        email: '',
        password: '',
        organizationName: '',
        organizationSlug: '',
        organizationDescription: '',
        adminName: '',
        adminEmail: '',
        adminPassword: '',
      });
    } catch (err: any) {
      console.error('Sign out error:', err);
    }
  };

  const handleSuccessOk = () => {
    window.open('https://ems-one-mauve.vercel.app/login', '_blank');
    handleClose();
  };

  const renderSuccessAlert = () => (
    <div className="space-y-6 text-center">
      <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
        <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
        </svg>
      </div>
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Setup Complete!</h2>
        <p className="text-gray-600">Your organization and admin account have been created successfully.</p>
      </div>
      <motion.button
        onClick={handleSuccessOk}
        className="w-full bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors duration-200"
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        OK - Go to Login
      </motion.button>
    </div>
  );

  const renderStep1 = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Create Your Account</h2>
        <p className="text-gray-600">Choose how you'd like to sign up</p>
      </div>

      {error && (
        <div className={`border px-4 py-3 rounded-lg ${isExistingUser
          ? 'bg-blue-50 border-blue-200 text-blue-700'
          : 'bg-red-50 border-red-200 text-red-700'
          }`}>
          {error}
          {isExistingUser && (
            <div className="mt-2">
              <a
                href="https://ems-one-mauve.vercel.app/login"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium"
              >
                Go to Login Page →
              </a>
            </div>
          )}
        </div>
      )}

      <motion.button
        onClick={handleGoogleSignup}
        disabled={loading}
        className="w-full flex items-center justify-center gap-3 bg-white border-2 border-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-50 hover:border-gray-400 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
        whileHover={{ scale: loading ? 1 : 1.02 }}
        whileTap={{ scale: loading ? 1 : 0.98 }}
      >
        {loading ? (
          <Loader2 className="w-5 h-5 animate-spin" />
        ) : (
          <img src="https://www.svgrepo.com/show/475656/google-color.svg" alt="Continue with Google" className="w-5 h-5" />)}
        Continue with Google
      </motion.button>

      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t border-gray-300" />
        </div>
        <div className="relative flex justify-center text-sm">
          <span className="px-2 bg-white text-gray-500">Or continue with email</span>
        </div>
      </div>

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Email Address
          </label>
          <div className="relative">
            <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
              placeholder="Enter your email"
              required
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Password
          </label>
          <div className="relative">
            <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="password"
              value={formData.password}
              onChange={(e) => handleInputChange('password', e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
              placeholder="Create a password"
              required
            />
          </div>
        </div>

        <motion.button
          onClick={handleNext}
          disabled={!formData.email || !formData.password || loading}
          className="w-full bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors duration-200 flex items-center justify-center gap-2"
          whileHover={{ scale: loading ? 1 : 1.02 }}
          whileTap={{ scale: loading ? 1 : 0.98 }}
        >
          {loading ? (
            <>
              <Loader2 className="w-4 h-4 animate-spin" />
              Creating Account...
            </>
          ) : (
            <>
              Continue
              <ArrowRight className="w-4 h-4" />
            </>
          )}
        </motion.button>
      </div>
    </div>
  );

  const renderStep2 = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Create Organization</h2>
        <p className="text-gray-600">Set up your organization details</p>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          {error}
        </div>
      )}

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Organization Name
          </label>
          <div className="relative">
            <Building className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              value={formData.organizationName}
              onChange={(e) => handleInputChange('organizationName', e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
              placeholder="Enter organization name"
              required
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Organization Slug
          </label>
          <div className="relative">
            <Globe className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              value={formData.organizationSlug}
              onChange={(e) => handleInputChange('organizationSlug', e.target.value.toLowerCase().replace(/[^a-z0-9-]/g, ''))}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
              placeholder="organization-slug"
              required
            />
          </div>
          <p className="text-xs text-gray-500 mt-1">This will be used in your organization URL</p>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Description
          </label>
          <textarea
            value={formData.organizationDescription}
            onChange={(e) => handleInputChange('organizationDescription', e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
            placeholder="Describe your organization"
            rows={3}
            required
          />
        </div>

        <div className="flex gap-3">
          <motion.button
            onClick={handleBack}
            className="flex-1 bg-gray-100 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-200 transition-colors duration-200 flex items-center justify-center gap-2"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <ArrowLeft className="w-4 h-4" />
            Back
          </motion.button>
          <motion.button
            onClick={handleNext}
            disabled={!formData.organizationName || !formData.organizationSlug || !formData.organizationDescription || loading}
            className="flex-1 bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors duration-200 flex items-center justify-center gap-2"
            whileHover={{ scale: loading ? 1 : 1.02 }}
            whileTap={{ scale: loading ? 1 : 0.98 }}
          >
            {loading ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin" />
                Creating...
              </>
            ) : (
              <>
                Continue
                <ArrowRight className="w-4 h-4" />
              </>
            )}
          </motion.button>
        </div>
      </div>
    </div>
  );

  const renderStep3 = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Admin Details</h2>
        <p className="text-gray-600">Set up the organization administrator</p>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          {error}
        </div>
      )}

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Admin Name
          </label>
          <div className="relative">
            <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              value={formData.adminName}
              onChange={(e) => handleInputChange('adminName', e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
              placeholder="Enter admin name"
              required
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Admin Email
          </label>
          <div className="relative">
            <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="email"
              value={formData.adminEmail}
              onChange={(e) => handleInputChange('adminEmail', e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
              placeholder="Enter admin email"
              required
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Admin Password
          </label>
          <div className="relative">
            <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="password"
              value={formData.adminPassword}
              onChange={(e) => handleInputChange('adminPassword', e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
              placeholder="Create admin password"
              required
            />
          </div>
        </div>

        <div className="flex gap-3">
          <motion.button
            onClick={handleBack}
            className="flex-1 bg-gray-100 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-200 transition-colors duration-200 flex items-center justify-center gap-2"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <ArrowLeft className="w-4 h-4" />
            Back
          </motion.button>
          <motion.button
            onClick={handleSubmit}
            disabled={!formData.adminName || !formData.adminEmail || !formData.adminPassword || loading}
            className="flex-1 bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors duration-200 flex items-center justify-center gap-2"
            whileHover={{ scale: loading ? 1 : 1.02 }}
            whileTap={{ scale: loading ? 1 : 0.98 }}
          >
            {loading ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin" />
                Completing Setup...
              </>
            ) : (
              'Complete Setup'
            )}
          </motion.button>
        </div>
      </div>
    </div>
  );

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        onClick={handleClose}
      >
        <motion.div
          className="bg-white rounded-2xl shadow-2xl w-full max-w-md max-h-[90vh] overflow-y-auto"
          initial={{ scale: 0.9, opacity: 0, y: 50 }}
          animate={{ scale: 1, opacity: 1, y: 0 }}
          exit={{ scale: 0.9, opacity: 0, y: 50 }}
          onClick={(e) => e.stopPropagation()}
        >
          <div className="p-6">
            <div className="flex justify-between items-center mb-6">
              <div className="flex items-center gap-2">
                <div className="flex space-x-2">
                  {[1, 2, 3].map((step) => (
                    <div
                      key={step}
                      className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${step === currentStep
                        ? 'bg-blue-600 text-white'
                        : step < currentStep
                          ? 'bg-green-500 text-white'
                          : 'bg-gray-200 text-gray-600'
                        }`}
                    >
                      {step}
                    </div>
                  ))}
                </div>
              </div>
              <button
                onClick={handleClose}
                className="text-gray-400 hover:text-gray-600 transition-colors duration-200"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            <AnimatePresence mode="wait">
              <motion.div
                key={success ? 'success' : currentStep}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
              >
                {success ? renderSuccessAlert() : (
                  <>
                    {currentStep === 1 && renderStep1()}
                    {currentStep === 2 && renderStep2()}
                    {currentStep === 3 && renderStep3()}
                  </>
                )}
              </motion.div>
            </AnimatePresence>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default SignupModal;
